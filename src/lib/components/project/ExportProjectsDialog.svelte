<script lang="ts">
  import * as Dialog from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import Select from "$lib/components/ui/select.svelte";
  import { FileDown, FileText, FileJson, Loader2, CheckCircle, AlertCircle, FolderOpen } from 'lucide-svelte';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { projectManagementService, type ProjectQuery } from '$lib/services/projectManagementService';
  import { onMount } from 'svelte';

  // 组件属性
  let { open = $bindable(false), projects = $bindable([]) } = $props();

  // 导出配置
  let exportFormat = $state('csv'); // 'csv' or 'json'
  let isExporting = $state(false);
  let exportResult = $state<{ success: boolean; message: string; filePath?: string } | null>(null);

  // 过滤器状态
  let selectedResearchStageId = $state<number | null>(null);
  let selectedDiseaseId = $state<number | null>(null);
  let isLoadingFilters = $state(false);
  let filterError = $state<string | null>(null);

  // 字典数据
  let researchStages = $state<{item_id: number, item_value: string}[]>([]);
  let diseases = $state<{item_id: number, item_value: string}[]>([]);

  // 预览数据
  let filteredProjectsCount = $state<number | null>(null);
  let isLoadingPreview = $state(false);

  // 加载字典数据
  async function loadDictionaryData() {
    try {
      isLoadingFilters = true;
      filterError = null;

      // 加载研究分期字典（注意：项目表中的project_stage_item_id实际指向研究分期，不是研究阶段）
      const researchStagesDict = await sqliteDictionaryService.getDictByName('研究分期');
      if (researchStagesDict && researchStagesDict.items) {
        researchStages = researchStagesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('研究分期字典加载成功:', researchStages);
      } else {
        console.warn('研究分期字典为空或未找到');
      }

      // 加载疾病字典
      const diseasesDict = await sqliteDictionaryService.getDictByName('疾病');
      if (diseasesDict && diseasesDict.items) {
        diseases = diseasesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('疾病字典加载成功:', diseases);
      } else {
        console.warn('疾病字典为空或未找到');
      }
    } catch (error) {
      console.error('加载字典数据失败:', error);
      filterError = `加载筛选条件失败: ${error instanceof Error ? error.message : '未知错误'}`;
    } finally {
      isLoadingFilters = false;
    }
  }

  // 获取过滤后的项目数据
  async function getFilteredProjects() {
    try {
      // 构建查询参数
      const query: ProjectQuery = {
        disease_item_id: selectedDiseaseId || undefined,
        project_stage_item_id: selectedResearchStageId || undefined,
        page: 1,
        page_size: 10000, // 获取所有匹配的项目
        sort_by: 'project_name',
        sort_order: 'asc'
      };

      // 获取过滤后的项目列表
      const result = await projectManagementService.getProjects(query);
      return result.items;
    } catch (error) {
      console.error('获取过滤项目失败:', error);
      throw error;
    }
  }

  // 预览过滤结果
  async function updatePreview() {
    if (!selectedResearchStageId && !selectedDiseaseId) {
      filteredProjectsCount = projects.length;
      return;
    }

    try {
      isLoadingPreview = true;
      const filteredProjects = await getFilteredProjects();
      filteredProjectsCount = filteredProjects.length;
    } catch (error) {
      console.error('预览过滤结果失败:', error);
      filteredProjectsCount = null;
    } finally {
      isLoadingPreview = false;
    }
  }

  // 监听过滤条件变化
  $effect(() => {
    updatePreview();
  });

  // 执行导出
  async function executeExport() {
    isExporting = true;
    exportResult = null;

    try {
      // 获取要导出的项目数据
      let projectsToExport;

      // 如果设置了过滤条件，则获取过滤后的数据
      if (selectedResearchStageId || selectedDiseaseId) {
        projectsToExport = await getFilteredProjects();
      } else {
        // 否则使用传入的项目数据
        projectsToExport = projects;
      }

      if (!projectsToExport || projectsToExport.length === 0) {
        exportResult = { success: false, message: '没有符合条件的项目数据可导出' };
        return;
      }

      // 准备导出数据
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filterSuffix = getFilterSuffix();
      const fileName = `项目列表${filterSuffix}_${timestamp}.${exportFormat}`;

      let content: string;
      if (exportFormat === 'csv') {
        content = generateCSVContent(projectsToExport);
      } else {
        content = generateJSONContent(projectsToExport);
      }

      // 调用后端导出功能
      const result = await fileSystemService.exportProjectsToFolder(content, fileName, exportFormat);

      if (result.success && result.data) {
        const filePath = result.data.file_path;
        exportResult = {
          success: true,
          message: `文件已成功导出到: ${filePath}`,
          filePath
        };
      } else {
        exportResult = {
          success: false,
          message: result.error || '导出失败'
        };
      }
    } catch (error) {
      console.error('导出失败:', error);
      exportResult = {
        success: false,
        message: `导出失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    } finally {
      isExporting = false;
    }
  }

  // 生成过滤条件后缀
  function getFilterSuffix(): string {
    const filters = [];
    if (selectedResearchStageId) {
      const stage = researchStages.find(s => s.item_id === selectedResearchStageId);
      if (stage) filters.push(stage.item_value);
    }
    if (selectedDiseaseId) {
      const disease = diseases.find(d => d.item_id === selectedDiseaseId);
      if (disease) filters.push(disease.item_value);
    }
    return filters.length > 0 ? `_${filters.join('_')}` : '';
  }

  // 生成CSV内容
  function generateCSVContent(projectsData: any[]): string {
    // 准备CSV标题行
    const headers = [
      '项目ID',
      '项目全称',
      '项目简称',
      '疾病',
      '研究分期',
      '项目状态',
      '招募状态',
      '启动日期'
    ];

    // 准备CSV数据行
    const rows = projectsData.map(project => [
      project.project.project_id || '',
      project.project.project_name || '',
      project.project.project_short_name || '',
      project.disease?.item_value || '',
      project.project_stage?.item_value || '',
      project.project_status?.item_value || '',
      project.recruitment_status?.item_value || '',
      project.project.project_start_date || ''
    ]);

    // 组合CSV内容
    return [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');
  }

  // 生成JSON内容
  function generateJSONContent(projectsData: any[]): string {
    // 准备JSON数据
    const jsonData = projectsData.map(project => ({
      project_id: project.project.project_id,
      project_name: project.project.project_name,
      project_short_name: project.project.project_short_name,
      disease: project.disease?.item_value,
      project_stage: project.project_stage?.item_value,
      project_status: project.project_status?.item_value,
      recruitment_status: project.recruitment_status?.item_value,
      project_start_date: project.project.project_start_date
    }));

    // 添加元数据
    const exportData = {
      metadata: {
        export_date: new Date().toISOString(),
        export_version: '1.0.0',
        total_projects: jsonData.length,
        filters: {
          research_stage: selectedResearchStageId ? researchStages.find(s => s.item_id === selectedResearchStageId)?.item_value : null,
          disease: selectedDiseaseId ? diseases.find(d => d.item_id === selectedDiseaseId)?.item_value : null
        }
      },
      projects: jsonData
    };

    return JSON.stringify(exportData, null, 2);
  }

  // 打开文件夹
  async function openExportFolder() {
    if (exportResult?.filePath) {
      try {
        // 获取文件所在目录
        const folderPath = exportResult.filePath.substring(0, exportResult.filePath.lastIndexOf('/'));
        await fileSystemService.openFolder(folderPath);
      } catch (error) {
        console.error('打开文件夹失败:', error);
      }
    }
  }

  // 重置状态
  function resetDialog() {
    exportResult = null;
    isExporting = false;
    selectedResearchStageId = null;
    selectedDiseaseId = null;
  }

  // 关闭对话框时重置状态
  $effect(() => {
    if (!open) {
      resetDialog();
    }
  });

  // 组件挂载时加载字典数据
  onMount(() => {
    loadDictionaryData();
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="max-w-md">
    <Dialog.Header>
      <Dialog.Title class="text-xl">导出项目数据</Dialog.Title>
      <Dialog.Description>
        选择导出格式并保存到指定位置
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      {#if !exportResult}
        <!-- 过滤器选择 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium mb-3">筛选条件</h3>

          <!-- 错误提示 -->
          {#if filterError}
            <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p class="text-sm text-red-700">{filterError}</p>
            </div>
          {/if}

          <div class="grid grid-cols-2 gap-4 mb-4">
            <!-- 研究分期过滤器 -->
            <div>
              <label class="text-xs text-gray-600 mb-1 block">研究分期</label>
              <Select
                bind:value={selectedResearchStageId}
                placeholder="选择研究分期"
                disabled={isLoadingFilters}
                class="w-full"
                let:select
              >
                <div
                  class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onclick={() => select(null, '全部阶段')}
                >
                  全部分期
                </div>
                {#each researchStages as stage}
                  <div
                    class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                    onclick={() => select(stage.item_id, stage.item_value)}
                  >
                    {stage.item_value}
                  </div>
                {/each}
              </Select>
            </div>

            <!-- 疾病过滤器 -->
            <div>
              <label class="text-xs text-gray-600 mb-1 block">疾病类型</label>
              <Select
                bind:value={selectedDiseaseId}
                placeholder="选择疾病类型"
                disabled={isLoadingFilters}
                class="w-full"
                let:select
              >
                <div
                  class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onclick={() => select(null, '全部疾病')}
                >
                  全部疾病
                </div>
                {#each diseases as disease}
                  <div
                    class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                    onclick={() => select(disease.item_id, disease.item_value)}
                  >
                    {disease.item_value}
                  </div>
                {/each}
              </Select>
            </div>
          </div>

          <!-- 过滤器状态提示和预览 -->
          {#if selectedResearchStageId || selectedDiseaseId}
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs text-blue-700 font-medium mb-1">已应用筛选条件：</p>
                  <p class="text-xs text-blue-600">
                    {#if selectedResearchStageId}
                      研究分期 = {researchStages.find(s => s.item_id === selectedResearchStageId)?.item_value}
                    {/if}
                    {#if selectedDiseaseId}
                      {selectedResearchStageId ? '，' : ''}疾病类型 = {diseases.find(d => d.item_id === selectedDiseaseId)?.item_value}
                    {/if}
                  </p>
                </div>
                <div class="text-right">
                  {#if isLoadingPreview}
                    <div class="flex items-center gap-1">
                      <Loader2 class="h-3 w-3 animate-spin text-blue-500" />
                      <span class="text-xs text-blue-500">计算中...</span>
                    </div>
                  {:else if filteredProjectsCount !== null}
                    <p class="text-xs text-blue-700 font-medium">
                      将导出 {filteredProjectsCount} 个项目
                    </p>
                  {/if}
                </div>
              </div>
            </div>
          {:else if filteredProjectsCount !== null}
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
              <p class="text-xs text-gray-600">
                将导出全部 {filteredProjectsCount} 个项目
              </p>
            </div>
          {/if}
        </div>

        <!-- 格式选择 -->
        <div class="mb-4">
          <h3 class="text-sm font-medium mb-3">导出格式</h3>
        </div>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div
            role="button"
            tabindex="0"
            class="border rounded-lg p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-gray-50 transition-colors {exportFormat === 'csv' ? 'border-blue-500 bg-blue-50' : ''}"
            onclick={() => exportFormat = 'csv'}
            onkeydown={(e) => e.key === 'Enter' && (exportFormat = 'csv')}
            aria-label="选择CSV格式"
          >
            <FileText class="h-12 w-12 text-blue-500" />
            <h3 class="font-medium">CSV 格式</h3>
            <p class="text-sm text-gray-500 text-center">导出为逗号分隔值文件，可在Excel等电子表格软件中打开</p>
          </div>

          <div
            role="button"
            tabindex="0"
            class="border rounded-lg p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-gray-50 transition-colors {exportFormat === 'json' ? 'border-blue-500 bg-blue-50' : ''}"
            onclick={() => exportFormat = 'json'}
            onkeydown={(e) => e.key === 'Enter' && (exportFormat = 'json')}
            aria-label="选择JSON格式"
          >
            <FileJson class="h-12 w-12 text-green-500" />
            <h3 class="font-medium">JSON 格式</h3>
            <p class="text-sm text-gray-500 text-center">导出为结构化JSON数据，包含完整的项目信息</p>
          </div>
        </div>

        <!-- 导出说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <p class="text-sm text-blue-700">
            点击"导出数据"后，系统将{#if selectedResearchStageId || selectedDiseaseId}根据筛选条件获取项目数据，然后{/if}打开文件保存对话框，您可以选择保存位置和文件名。
          </p>
        </div>
      {:else}
        <!-- 导出结果 -->
        <div class="text-center py-4">
          {#if exportResult.success}
            <div class="flex flex-col items-center gap-3">
              <CheckCircle class="h-12 w-12 text-green-500" />
              <h3 class="font-medium text-green-700">导出成功</h3>
              <p class="text-sm text-gray-600 break-all">{exportResult.message}</p>
              {#if exportResult.filePath}
                <Button variant="outline" onclick={openExportFolder} class="gap-2">
                  <FolderOpen class="h-4 w-4" />
                  打开文件夹
                </Button>
              {/if}
            </div>
          {:else}
            <div class="flex flex-col items-center gap-3">
              <AlertCircle class="h-12 w-12 text-red-500" />
              <h3 class="font-medium text-red-700">导出失败</h3>
              <p class="text-sm text-gray-600">{exportResult.message}</p>
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <div class="flex justify-between w-full">
        <Button variant="outline" onclick={() => open = false}>
          {exportResult ? '关闭' : '取消'}
        </Button>
        {#if !exportResult}
          <Button onclick={executeExport} disabled={isExporting} class="gap-2">
            {#if isExporting}
              <Loader2 class="h-4 w-4 animate-spin" />
              导出中...
            {:else}
              <FileDown class="h-4 w-4" />
              导出数据
            {/if}
          </Button>
        {:else if !exportResult.success}
          <Button onclick={resetDialog} variant="outline">
            重试
          </Button>
        {/if}
      </div>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
